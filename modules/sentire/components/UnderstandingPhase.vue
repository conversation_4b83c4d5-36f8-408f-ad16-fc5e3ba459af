<script setup lang="ts">

import { useCurrentThreadStore } from "../stores/currentThreadStore";
import type { TextMessage, ToolCallMessage } from "~/types/message";
import InteractiveToolCall from "./InteractiveToolCall.vue";
import StandardToolCall from "@/components/StandardToolCall.vue";
import LookupCluesTool from "./LookupCluesTool.vue";
import Markdown from "@/components/Markdown.vue";
import {
  isText,
  isInteractiveToolCallStart,
  isToolCall,
  isLookupCluesToolCall,
  isArtifactReportToolCall,
  isPlanToolCall,
} from "@/hooks/agent/message";
import { TOOL_CALL_NAMES } from "../const";
import { Button } from "@/components/ui/button";
import { ChevronDownIcon, ChevronUpIcon, Webhook } from "lucide-vue-next";
import ArtifactReportToolCall from "./ArtifactReportToolCall.vue";
import type { Clue } from "~/types/sentire/clue";
import { ref } from "vue";
import HiddenToolCall from "@/components/HiddenToolCall.vue";

const contentRef = ref<HTMLElement | null>(null);
const currentThreadStore = useCurrentThreadStore();
const {
  summary,
  isAgentAnalyzing,
  isRunning,
  understandingStepsCollapsed,
  steps,
} = storeToRefs(currentThreadStore);

const clues = ref<Clue[]>([]);
const onCluesLoaded = (newClues: Clue[]) => {
  clues.value = newClues;
};

const contentMaxHeight = ref("");
const toolCallMaxHeight = ref(false);

const stepHeights = ref<Record<string, number>>({});
const stepContentRefs = ref<Record<string, HTMLElement>>({});
const observers = ref<
  Record<string, { resize: ResizeObserver; mutation: MutationObserver }>
>({});
const buttonIsShow = ref(true);

// 监控步骤内容高度变化
const updateStepHeight = (stepId: string, element: HTMLElement) => {
  if (element && !observers.value[stepId]) {
    stepContentRefs.value[stepId] = element;

    const updateHeight = () => {
      nextTick(() => {
        const height = element.offsetHeight;
        if (height > 0) {
          stepHeights.value[stepId] = height;
        }
      });
    };

    updateHeight();

    // 使用 ResizeObserver 监控高度变化
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const height = entry.contentRect.height;
        if (height > 0) {
          stepHeights.value[stepId] = height;
        }
      }
    });

    resizeObserver.observe(element);

    // 监控内容变化
    const mutationObserver = new MutationObserver(() => {
      updateHeight();
    });

    mutationObserver.observe(element, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true,
    });

    // 存储观察器
    observers.value[stepId] = {
      resize: resizeObserver,
      mutation: mutationObserver,
    };
  }
};

const getLineHeight = (stepId: string, hasNextStep: boolean) => {
  const height = stepHeights.value[stepId];
  return hasNextStep ? `${height + 24}px` : `${height}px`;
};

onUnmounted(() => {
  Object.values(observers.value).forEach(({ resize, mutation }) => {
    resize.disconnect();
    mutation.disconnect();
  });
});

const toolHeightChange = () => {
  if (summary.value && !understandingStepsCollapsed.value) {
    toolCallMaxHeight.value = true;
  }
  nextTick(() => {
    requestAnimationFrame(() => {
      buttonIsShow.value = (contentRef.value?.scrollHeight ?? 0) > 400;
    });
  });
};

watch(
  [understandingStepsCollapsed, toolCallMaxHeight, summary],
  ([newCollapsed, newToolCallMaxHeight, newTitleSummary]) => {
    if (!newCollapsed) {
      nextTick(() => {
        if (contentRef.value || newToolCallMaxHeight) {
          const actualHeight = contentRef.value?.scrollHeight + "px";
          contentMaxHeight.value = actualHeight;

          if (newToolCallMaxHeight) {
            toolCallMaxHeight.value = false;
          }
        } else {
          contentMaxHeight.value = "";
        }
      });
    } else if (newCollapsed && newTitleSummary) {
      contentMaxHeight.value = "400px";
      buttonIsShow.value = (contentRef.value?.scrollHeight ?? 0) > 400;
    }
  }
);

const showStepLine = (messages: readonly (TextMessage | ToolCallMessage)[]) => {
  if (!messages || messages.length === 0) return false;
  if (messages[0].type === "text" && messages.length === 1) {
    return (messages[0] as TextMessage).text.length > 0;
  }
  return true;
};
</script>

<template>
  <div
    v-if="steps.length"
    class="my-6 border rounded-lg-new border-bordercolor p-3"
  >
    <div
      class="bg-title-bg items-center justify-between rounded-lg py-1 px-2 flex mb-3"
      :class="{
        hidden: !summary,
      }"
    >
      <div class="flex items-center gap-4">
        <Webhook class="h-4 w-4 text-primary" />
        <div class="text-sm font-medium leading-snug mb-2 text-default-text">
          {{ summary }}
        </div>
      </div>
      <Button
        :style="buttonIsShow ? '' : 'visibility: hidden;'"
        variant="ghost"
        size="sm"
        class="w-9 p-0 cursor-pointer"
        @click="currentThreadStore.toggleUnderstandingStepsCollapsed()"
      >
        <ChevronDownIcon v-if="understandingStepsCollapsed" class="h-4 w-4" />
        <ChevronUpIcon v-else class="h-4 w-4" />
      </Button>
    </div>
    <div
      ref="contentRef"
      class="content-container transition-all duration-500 ease-in-out overflow-hidden"
      :class="{
        collapsed: understandingStepsCollapsed,
        expanded: !understandingStepsCollapsed,
      }"
      :style="contentMaxHeight ? { maxHeight: contentMaxHeight } : {}"
    >
      <div class="flex w-full flex-col justify-start gap-6 my-6">
        <div
          v-for="(step, index) in steps"
          :key="step.id"
          class="flex flex-col gap-4 w-full"
        >
          <div
            :ref="el => el && updateStepHeight(String(step.id), el as HTMLElement)"
            class="flex flex-col gap-4 w-full min-w-0"
          >
            <div
              :class="[step.status === 'running' && 'text-primary']"
              class="text-sm font-medium leading-snug mb-2 transition"
            >
              <Markdown
                :source="TOOL_CALL_NAMES[step.title as keyof typeof TOOL_CALL_NAMES] ?? step.title"
                class="prose-p:text-default-text font-normal !prose-code:text-sm prose-p:truncate"
              />
            </div>
            <div
              :class="[step.status === 'running' && 'text-primary']"
              class="text-xs leading-snug text-muted-foreground transition lg:text-sm flex flex-col space-y-4"
            >
              <div
                v-for="(content, contentIndex) in step.messages"
                :key="contentIndex"
              >
                <InteractiveToolCall
                  v-if="isInteractiveToolCallStart(content)"
                  :question="(content as ToolCallMessage).args.question as string"
                  :type="(content as ToolCallMessage).args.type as string"
                  :options="(content as ToolCallMessage).args.options as string[] ?? []"
                  @tool-height-change="toolHeightChange"
                  @submit="
            (value) =>
              currentThreadStore.confirmInteractiveResult(
                (content as ToolCallMessage).toolName,
                value
              )
          "
                />
                <HiddenToolCall v-else-if="isPlanToolCall(content)" />
                <StandardToolCall
                  v-else-if="isToolCall(content)"
                  :name="(content as ToolCallMessage).toolName"
                  :input="(content as ToolCallMessage).args"
                  :output="(content as ToolCallMessage).result as Record<string, unknown>"
                  :elapsed-time="(content as ToolCallMessage).elapsedTime"
                  @tool-height-change="toolHeightChange"
                />
                <LookupCluesTool
                  v-if="isLookupCluesToolCall(content)"
                  :step-index="index"
                  @clues-loaded="onCluesLoaded"
                  @tool-height-change="toolHeightChange"
                />
                <ArtifactReportToolCall
                  v-if="isArtifactReportToolCall(content)"
                  class="mt-2"
                  :report-id="(content as ToolCallMessage).result?.id  as string"
                  :status="(content as ToolCallMessage).result?.status as string"
                  :title="(content as ToolCallMessage).result?.title as string"
                  :message="(content as ToolCallMessage).result?.message as string"
                />
                <div
                  v-if="isText(content) && (content as TextMessage).text.length > 0"
                >
                  <Markdown
                    :source="(content as TextMessage).text"
                    class="text-xs leading-snug prose-p:text-default-text font-normal prose-code:text-xs mb-4"
                  />
                </div>
              </div>
            </div>
            <!-- Pinwheel at the bottom of running step content -->
            <ClientOnly>
              <l-pinwheel
                v-if="step.status === 'running' && isAgentAnalyzing && !summary"
                size="20"
                speed="1.3"
                color="oklch(0.5613 0.0924 238.72)"
                class="mt-2"
              />
            </ClientOnly>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>
<style scoped lang="scss">
.content-container {
  transition: max-height 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    overflow-y: auto;
    overflow-x: visible;
    display: flex;
    flex-direction: column-reverse;
  }
  &.expanded {
    overflow-y: auto;
    overflow-x: visible;
    display: flex;
    flex-direction: column;

    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.step-line {
  animation: lineGrow 0.4s ease-out forwards;
  transform-origin: top;
  transform: scaleY(0);
  opacity: 0;
  transition: height 0.2s ease-out;
  will-change: height, transform, opacity;
}

@keyframes lineGrow {
  0% {
    transform: scaleY(0);
    opacity: 0;
  }
  20% {
    opacity: 0.3;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.step-dot {
  animation: dotAppear 0.4s ease-out forwards;
  transform: scale(0);
  opacity: 0;
}

@keyframes dotAppear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.step-content {
  animation: contentFadeIn 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-enter-active,
.step-leave-active {
  transition: opacity 0.5s;
}
.step-enter,
.step-leave-to {
  opacity: 0;
}
</style>
